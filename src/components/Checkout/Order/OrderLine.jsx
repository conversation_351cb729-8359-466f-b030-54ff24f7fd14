import { useState, useRef, useEffect } from 'react';

import { delay } from 'utils/delay';

import { OrderLineImage, OrderLineQuantity } from 'components/Checkout';
import useCheckoutStore from 'store/useCheckoutStore';
import useCustomOrderStore from 'store/useCustomOrderStore';

const OrderLine = ({ orderLine, customOrderConfig = false }) => {
  const isMounted = useRef(false);

  const { updateOrderLine, removeOrderLine } = useCheckoutStore((state) => ({
    updateOrderLine: state.updateOrderLine,
    removeOrderLine: state.removeOrderLine,
  }));
  const { editOrderLine, cloneOrderLine } = useCustomOrderStore((state) => ({
    editOrderLine: state.editOrderLine,
    cloneOrderLine: state.cloneOrderLine,
  }));
  const [localOrderLine, setLocalOrderLine] = useState(orderLine);
  const isBlankOrSameQuantity = orderLine.quantity === localOrderLine.quantity || localOrderLine.quantity === '';
  const isZeroQuantity = Number(localOrderLine.quantity) === 0;

  const handleUpdate = (event) => {
    setLocalOrderLine((state) => ({ ...state, [event.target.name]: event.target.value }));
  };

  const onBlur = async () => {
    updateOrderLine(localOrderLine);
  };

  // Update Zustand Order Line if the quantity is not 0 or the same as local state
  useEffect(async () => {
    if (isMounted.current) {
      if (isBlankOrSameQuantity) return;
      if (isZeroQuantity) {
        try {
          await removeOrderLine(orderLine);
        } catch (err) {
          if (localOrderLine.prevQuantity) {
            setLocalOrderLine((state) => ({ ...state, quantity: state.prevQuantity }));
          }
        }
        return;
      }
      return delay(function () {
        updateOrderLine(localOrderLine);
      }, 300);
    }
    isMounted.current = true;
  }, [localOrderLine.quantity]);

  const handleRemove = async () => {
    try {
      await removeOrderLine(orderLine);
    } catch (err) {
      // do nothing
    }
  };

  const handleEdit = () => {
    const { location, supplier } = customOrderConfig;
    editOrderLine({ orderLine, location, supplier });
  };

  const handleClone = () => {
    const { location, supplier } = customOrderConfig;
    cloneOrderLine({ orderLine, location, supplier });
  };

  // Custom order config layout
  if (customOrderConfig) {
    const customItem = orderLine.custom_item || {};

    return (
      <div className={`orderline ${orderLine.errors ? 'errored' : ''} custom-orderline`}>
        <div className="order-show-table-row custom-order">
          <div className="orderline-image-name">
            <OrderLineImage
              name={orderLine.name}
              quantity={orderLine.quantity}
              image={orderLine.image}
              showCount={false}
            />
            <div className="orderline-name-container">
              <p className="orderline-name">{orderLine.name}</p>
              {orderLine.is_reduced_price && <span className="special-checkout-price">special</span>}
            </div>
          </div>

          {/* Pricing fields in horizontal layout */}
          <div className="pricing-value">${customItem.baseline || '0.00'}</div>

          {!customItem.is_gst_free ? (
            <div className="pricing-value">${customItem.baseline_inc_gst || '0.00'}</div>
          ) : (
            <div className="pricing-value">-</div>
          )}

          <div className="pricing-value">${customItem.cost || '0.00'}</div>

          <div className="pricing-value">${customItem.price || '0.00'}</div>

          <p>Qty: {orderLine.quantity}</p>

          <p className={orderLine.is_reduced_price ? ' reduced' : ''}>
            <strong>${(Number(orderLine.order_line_value) * orderLine.quantity).toFixed(2)}</strong>
          </p>

          <span
            className="orderline-clone icon icon-large icon-copy"
            role="presentation"
            onClick={handleClone}
            style={{
              cursor: 'pointer',
              fontSize: '16px',
              color: '#1f9e86',
              borderRadius: '4px',
            }}
            title="Clone item"
          />
          <span
            className="orderline-edit icon icon-large icon-pencil-black"
            role="presentation"
            onClick={handleEdit}
            style={{
              cursor: 'pointer',
              fontSize: '16px',
              color: '#1f9e86',
              borderRadius: '4px',
            }}
            title="Edit item"
          />
          <span className="orderline-delete icon icon-large icon-bin-dark" role="presentation" onClick={handleRemove} />

          {orderLine.errors && <p className="error-message">{orderLine.errors.join(',')}</p>}
        </div>
      </div>
    );
  }

  // Regular order line layout
  return (
    <div className={`orderline ${orderLine.errors ? 'errored' : ''}`}>
      <div className="order-show-table-row">
        <div className="orderline-image-name">
          <OrderLineImage name={orderLine.name} quantity={orderLine.quantity} image={orderLine.image} />
          <div style={{ width: '100%' }}>
            <div className="orderline-name-container">
              <p className="orderline-name">{orderLine.name}</p>
              {orderLine.is_reduced_price && <span className="special-checkout-price">special</span>}
            </div>

            <div className="extras">
              {orderLine.extrasString && (
                <span className="grey" title={orderLine.extrasString}>
                  {orderLine.extrasString}
                </span>
              )}
            </div>
            <input
              className="orderline-note grey"
              name="note"
              value={localOrderLine.note}
              onChange={handleUpdate}
              onBlur={onBlur}
              placeholder="Add a note"
            />
            {orderLine.errors && <p className="error-message">{orderLine.errors.join(',')}</p>}
          </div>
        </div>
        <p style={{ justifySelf: 'flex-end' }}>{`$${Number(orderLine.order_line_value).toFixed(2)}`}</p>
        <OrderLineQuantity
          orderLine={localOrderLine}
          originalQuantity={orderLine.quantity}
          updateItem={setLocalOrderLine}
          onBlur={onBlur}
        />
        <p className={orderLine.is_reduced_price ? ' reduced' : ''}>
          <strong>${(Number(orderLine.order_line_value) * orderLine.quantity).toFixed(2)}</strong>
        </p>
        <span className="orderline-delete icon icon-large icon-bin-dark" role="presentation" onClick={handleRemove} />
      </div>
    </div>
  );
};

export default OrderLine;
