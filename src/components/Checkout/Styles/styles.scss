.checkout {
  padding: 40px 40px 100px;
  max-width: 1400px;
  margin: auto;
  @include media-down(large-tablet) {
    padding: 40px 8px;
  }
  p {
    font-size: 14px;
  }
  @include media-down(large-tablet) {
    p {
      font-size: 16px;
    }
  }
}

.react-responsive-modal-overlay {
  .Calendar {
    width: 100% !important;
    flex: 1;
    border: 1px solid rgb(219, 219, 219);
    max-height: 330px;
    min-height: auto !important;
    &__weekDays {
      margin-bottom: 0 !important;
    }
  }
}

.checkout-container {
  display: grid;
  grid-template-columns: 3fr 2fr;
  grid-column-gap: 20px;
  margin: auto;
  .custom-order {
    .icon::before {
      margin-right: 0;
    }
  }
  .icon::before {
    margin-right: 16px;
  }
  @include media-down(large-tablet) {
    grid-template-columns: 1fr;
    margin-bottom: 30px;
    .button {
      margin-top: 0;
    }
  }
}

.order-progress {
  display: flex;
  justify-content: space-around;
  border: 1px solid #cacaca;
  border-bottom: none;
  // text-align: center;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  h3 {
    flex: 1;
    padding: 12px 36px;
    font-size: 14px;
    cursor: pointer;
    background: #f9f9f9;
    text-transform: uppercase;
    &:first-of-type {
      border-top-left-radius: 8px;
    }
    &:last-of-type {
      border-top-right-radius: 8px;
    }
    &:hover {
      text-decoration: underline;
    }
    & + h3 {
      border-left: 1px solid #cacaca;
    }
  }
  h3.active {
    position: relative;
    background:white;
    color: #1f9e86;
  }
}

.checkout-form {
  background: white;
  padding: 0px 36px 20px;
  border: 1px solid #dbdbdb;
  border-bottom-right-radius: 8px;
  border-bottom-left-radius: 8px;
  &.edit-form {
    margin-bottom: 20px;
  }
}

.checkout-section {
  & + .checkout-section {
    border-top: none;
  }
}

.checkout-docket {
  align-self: flex-start;
  position: sticky;
  top: 70px;
  border-radius: 8px;
  &.no-sticky {
    position: static;
    margin: 20px 0;
  }
  .checkout-button {
    width: 100%;
    background: black;
    padding: 12px;
    font-size: 16px;
    font-weight: bold;
    &:hover {
      background: #111;
    }
    &.confirm {
      margin-top: 28px;
      &.primary {
        &:hover {
          background: darken($primary, 5%);
        }
      }
      &.secondary {
        background: gray;
        &:hover {
          background: darken(gray, 5%);
        }
      }
      &.tertiary {
        border: 1px solid black;
        background: transparent;
        color: black;
        &:hover {
          background: darken(white, 5%);
        }
      }
      &.cancel {
        background: #e42217;
        color: white;
        &:hover {
          background: darken(#e42217, 5%);
        }
      }
      &:hover {
        background: #111;
      }
    }
    &.submit {
      margin-top: 28px;
      &:hover {
        background: darken($primary, 5%);
      }
    }
  }
}

.checkout-docket-info {
  background: white;
  border-radius: 8px;
  border: 1px solid #dbdbdb;
  padding: 16px 20px;
  a.text-primary {
    color: $primary;
    cursor: pointer;
  }
}

.checkout-docket-section {
  border-bottom: 2px solid #eaeaea;
  padding: 20px 0;
  &:first-child {
    padding-top: 0;
  }
  &:last-child {
    border: none;
    padding-bottom: 0;
  }
}

.heading-block {
  border-top: 2px solid #eaeaea;
  margin-left: -36px;
  margin-right: -36px;
  padding: 24px 36px;
}

.section-heading {
  font-size: 22px;
  font-weight: 900;
  &.active {
    color: $primary;
  }
  &.flex {
    display: flex;
    justify-content: space-between;
  }
}

.bold {
  font-weight: bold;
}

.modal-section {
  display: flex;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid #d8d8d8;
  border-top: 1px solid #dfdfdf;
  &:first-of-type {
    border-top: none;
    padding-top: 0;
  }
  &:last-of-type {
    border-bottom: none;
  }
  @include media-down(large-tablet) {
    display: grid;
    grid-template-columns: 4fr 1fr;
  }
  & + .modal-section {
    border-top: none;
  }
  p {
    margin: 0;
    margin-right: 22px;
  }
  button {
    margin-left: auto;
  }
  .icon-extra-large::before {
    min-width: 22px;
    min-height: 22px;
  }
}

.quick-cart-view-btn {
  padding: 6px 10px;
  border-radius: 20px;
  background: $primary;
  color: white;
  border: none;
  font-family: $body-font;
}

.cart-supplier-name {
  margin-bottom: 0;
  max-width: 210px;
}

.checkout-button {
  align-self: center;
  padding: 4px 12px;
  text-transform: uppercase;
  font-weight: bold;
  font-family: $heading-font;
  font-size: 12px;
  min-width: 100px;
  border: none;
  cursor: pointer;
  &:hover {
    background: $off-black;
    color: white;
    transition: background 0.2s ease-in-out;
  }
  &.rounded {
    border-radius: 20px;
  }
  &.active {
    background: $primary;
    color: white;
    border: none;
  }
  &.disabled {
    background: #cecece;
    color: white;
    border: none;
    cursor: not-allowed;
  }
  &.primary {
    background: $primary;
    color: white;
    border: none;
  }
}

.supplier-menu-link {
  color: black;
  font-weight: 700;
}

.checkout-row {
  display: flex;
  align-items: center;
  &.icon::before {
    margin-right: 16px;
  }
}

.checkout-details {
  display: grid;
  grid-template-columns: 1fr 140px;
  grid-gap: 12px 20px;
  padding-bottom: 22px;
  margin-top: 16px;
  .checkout-detail {
    .title {
      // font-family: $heading-font;
      font-weight: bold;
      font-size: 14px;
      margin-bottom: 8px;
      // color: #7C7C7C;
    }
    input {
      width: 100%;
      padding: 12px;
      box-sizing: border-box;
      font-family: $body-font;
      border: 1px solid #b4b4b4;
      cursor: text;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }
}

.order-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  &--edit {
    grid-template-columns: 1fr 1fr 1fr;
  }
  grid-gap: 8px 16px;
  margin-bottom: 40px;
  .checkout-detail.full-width {
    grid-column: span 2;
  }
  @include media-down(large-tablet) {
    .checkout-detail:first-of-type {
      grid-column: span 1;
    }
    grid-template-columns: 1fr;
  }
  p {
    margin-bottom: 2px;
    font-family: 'Museo Slab';
    font-size: 14px;
  }
}

.invoice-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 12px 26px;
  margin-bottom: 40px;
  p {
    margin-bottom: 2px;
    font-family: 'Museo Slab';
    font-size: 14px;
  }
}

.payment-methods {
  display: flex;
  margin-bottom: 20px;
  > div {
    padding: 8px;
    border: 1px solid rgb(199, 199, 199);
    cursor: pointer;
    margin-right: 8px;
    border-radius: 4px;
    flex-basis: 180px;
    background: #eeeeee;
    min-width: 200px;
    & + div {
      margin-left: 12px;
    }
    p {
      margin-bottom: 0;
      line-height: 12px;
      margin-left: 8px;
      font-size: 16px;
    }
  }
  .active {
    border: 2px solid #000;
    background: white;
    > p {
      color: black;
      font-weight: bold;
    }
  }
}

.credit-card-container {
  margin-bottom: 20px;
}


.card-element {
  padding: 10px 20px;
  width: 100%;
  padding: 12px;
  box-sizing: border-box;
  font-family: "Museo Sans";
  border: 1px solid #b4b4b4;
  cursor: text;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.saved-cards-select {
  margin-bottom: 20px;
}

.checkout-coupon {
  display: grid;
  grid-template-columns: 3fr 1fr;
  font-size: 14px;
}

.totals-value {
  display: flex;
  justify-content: space-between;
  p {
    font-size: 16px;
    line-height: 20px;
    margin-bottom: 12px;
  }
  &.grey {
    color: #5e5e5e;
  }
  &.highlight {
    color: $primary;
  }
  &.bold {
    font-weight: bold;
  }
  &.total {
    border-top: 2px solid #eaeaea;
    padding-top: 18px;
    p {
      font-weight: 900;
      font-size: 20px;
      margin-bottom: 0;
    }
  }
}

.checkout-order {
  background: white;
  border: 1px solid #e1e1e1;
  border-radius: 4px;
  align-self: start;
  .customer-order__heading {
    font-family: $body-font;
    background: #ffe3c920;
    border-bottom: 1px solid #e9e9e9;
    padding: 10px 16px;
    text-align: center;
    font-weight: bold;
    font-size: 16px;
  }
  .supplier-name {
    color: #8c8c8c;
    margin: 16px 0 0 20px;
    color: #707070;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 12px;
    margin: 16px 0 0px 20px;
  }
  .order-show-table-row {
    display: grid;
    grid-template-columns: 14fr 2fr 4fr 2fr 1fr;
    grid-gap: 10px;
    align-items: start;
    font-size: 14px;
    color: #333333;
    line-height: 20px;
    margin-left: 0;
    &.custom-order {
      grid-template-columns: 8fr 3fr 3fr 3fr 3fr 2fr 3fr 1fr 1fr 1fr;
      align-items: center;
    }
    @include media-down(hamburger) {
      grid-template-columns: 1fr;
    }
    p {
      margin-bottom: 0;
    }
    .circle-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 50px;
      height: 50px;
      background: #3c3c3c;
      font-size: 16px;
      margin-right: 20px;
      color: white;
      &.small {
        min-width: 36px;
        width: 36px;
        height: 36px;
        font-size: 16px;
        color: black;
      }
      @include media-down(hamburger) {
        display: none;
      }
    }
    .orderline-count {
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      top: -6px;
      right: -12px;
      background: #000000b0;
      color: white;
      border-radius: 50px;
      padding: 0px 13px;
      font-size: 12px;
      line-height: 16px;
      @include media-down(hamburger) {
        display: none;
      }
    }
  }
  .orderline {
    padding: 1rem 16px;
    border-bottom: 1px solid #e9e9e9;
    &.custom-orderline {
      padding: 1rem 0;
      .orderline-image {
        width: 30px;
        height: 30px;
        min-width: 30px;
        max-width: 30px;
      }
      p {
        font-size: 12px;
      }
    }
    &:last-of-type {
      border-bottom: none;
    }
    &.errored {
      .error-message {
        color: #f82600;
      }
    }
  }
  .orderline-image-name {
    display: flex;
    position: relative;
  }
  .orderline-image {
    position: relative;
    margin-right: 20px;
    min-width: 44px;
    max-width: 44px;
    height: 44px;
  }
  .orderline-name {
    margin-right: 30px;
    font-weight: bold;
  }
  .extras {
    font-size: 12px;
    cursor: help;
  }
  .orderline-note {
    font-size: 14px;
    margin: 0;
    margin-top: 4px;
    background: white;
    width: 100%;
    padding: 4px 10px;
    border-radius: 4px;
    border: 1px solid #ededed;
    font-style: italic;
    @include media-down(hamburger) {
      margin-left: 0;
    }
  }

  // Custom order pricing styles for horizontal layout
  .pricing-value {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #333;
    font-weight: 600;
    text-align: center;
  }
  .grey {
    color: #767676;
  }
}

.recurring-day {
  display: flex;
  text-align: center;
  p {
    padding: 4px 0;
    margin: 0;
    cursor: pointer;
    font-weight: bold;
    border: 1px solid #cecece;
    &.active {
      background: black;
      color: white;
      border: none;
    }
  }
}

.date-time-container {
  display: grid;
  grid-template-columns: 5fr 2fr;
  grid-column-gap: 10px;
  padding-bottom: 20px;
}

.time-slot-list {
  max-height: 330px;
  overflow-y: auto;
  flex: 1;
  padding: 6px;
  border: 1px solid rgb(219, 219, 219);
  background: white;
  &__item {
    padding: 5px;
    cursor: pointer;
    background-color: transparent;
    border-radius: 4px;
    color: #000;
    &.active {
      background-color: #000;
      color: #ffffff;
    }
  }
}


.align {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.docket-icon {
  width: 44px;
  height: 44px;
  background: #3c3c3c;
  font-size: 16px;
  margin-right: 10px;
  color: white;
  object-fit: cover;
}

.checkout-stripe-button {
  padding: 12px 60px;
  background: $primary;
  color: white;
  border: none;
  border-radius: 4px;
  margin-top: 16px;
  font-size: 16px;
}

.coupon.coupon {
  padding: 10px 16px;
  box-shadow: none;
  border: 1px solid #d7d7d7;
  font-size: 14px;
  + .button {
    white-space: nowrap;
    background: white;
    border: 1px solid $primary;
    color: $primary;
    padding: 4px 8px;
    font-size: 12px;
  }
  &:focus {
    border-color: black;
  }
}

.stripe-submit-button {
  background: $primary;
  font-size: 16px;
  border:none;
  color: white;
  padding: 8px 20px;
  margin-top: 30px;
  font-family: $body-font;
}

.checkout-field {
  display: flex;
  flex-direction: column;
}

.checkout-modal-input {
  padding: 8px;
  margin-top: 20px;
  font-family: $body-font;
}

.checkout-address {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.checkout-address-form {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  .button {
    margin-top: auto;
  }
}

.address-label {
  display: flex;
  align-content: center;
  font-size: 18px;
  margin-bottom: 8px;
  &::before {
    margin-right: 8px;
  }
}

.saved-address-list {
  overflow: scroll;
  max-height: 400px;
}

input.location-search-input {
  width: 100%;
  border-radius: 4px;
  box-shadow: none!important;
  padding: 16px 10px;
  background-repeat: no-repeat;
  &.icon {
    background-image: url(../../../images/icons/marker-filled.svg);
    background-size: 14px;
    background-position: 2% 50%;
    background-color: #f5f5f5;
    padding: 16px 40px;
  }
  @include media-down(tablet) {
    box-shadow: none;
  }
}

.checkout-detail-number {
  display: grid;
  grid-template-columns: 1fr 4fr 1fr;
  @include media-down(large-tablet) {
    grid-template-columns: 1fr 2fr 1fr;
  }
  .number-control {
    border: 1px solid #CECECE;
    display: grid;
    place-items: center;
    margin-bottom: 0;
    cursor: pointer;
    transition: .1s background ease-in;
    box-shadow: none;
    background: #f7f7f7;
    &:first-of-type {
      border-top-left-radius: 8px !important;
      border-bottom-left-radius: 8px !important;
    }
    &:last-of-type {
      border-top-right-radius: 8px !important;
      border-bottom-right-radius: 8px !important;
    }
    &.invalid {
      border: 1px solid red;
    }
    &:hover {
      background: rgb(221, 221, 221);
    }
  }
}

.saved-address {
  display: grid;
  grid-template-columns: 50px auto 50px;
  border: 1px solid rgb(202, 202, 202);
  padding: 6px 10px;
  margin: 10px 0;
  border-radius: 4px;
  line-height:  20px;
  cursor: pointer;
  &:hover {
    background: #f5f5f5;
  }
  &::before {
    content: '';
    display: inline-block;
    background: url('../../../images/icons/marker-filled.svg');
    background-size: contain;
    width: 20px;
    height: 20px;
    margin-right: 10px;
    background-repeat: no-repeat;
    align-self: center;
  }
  p {
    margin-bottom: 4px;
  }
  p.bold {
    font-weight: bold;
  }
  p.grey {
    color: #717171;
  }
}

.edit-saved-address {
  align-self: center;
  &::before {
    content: '';
    display: inline-block;
    width: 24px;
    height: 24px;
    filter: invert(1);
    background: url('../../../images/icons/pencil.svg')
  }
}

.checkout-address-input input, input.checkout-address-input {
  border: 1px solid black;
  margin: 8px 0px;
}
textarea.checkout-address-input {
  border: 1px solid black;
  margin: 8px 0px;
}

.checkout-datepicker-container {
  .react-datepicker-wrapper {
    display: none;
  }
  .react-datepicker-triangle {
    display: none;
  }
  .react-datepicker-popper {
    position: static !important;
    transform: unset !important;
  }
}

.po-select {
  margin-bottom: 16px;
  font-size: 14px;
}

.notice {
  background: #f3f3f3;
  padding: 12px;
  margin: 12px 0;
  font-size: 14px;
  line-height: 20px;
}

.delivery-datepicker {
  padding: 12px 16px !important;
  display: none;
}

.billing-title {
  font-size: 18px !important;
  font-weight: bold;
  margin-top: 26px;
  margin-bottom: 10px;
}

.delivery-date-error {
  color: #fa5361;
  margin: 0px 0 8px;
  font-weight: bold;
}

.date-back {
  width: 100%;
  margin-top: 12px;
  line-height: normal;
}

.disable-date.disable-date {
  background: grey;
  border: none;
  &:hover {
    background: darken(grey, 5%);
    cursor: not-allowed;
  }
}

.cost-centre-input.cost-centre-input {
  padding: 12px;
  margin-bottom: 20px;
}

.icon-chevron-down.rotate {
  transform: rotate(180deg);
  transition: transform 0.3s ease;
}

.accordion-content {
  padding: 10px 0;
}

.needs-validation {
  opacity: 1;
  border-radius: 9px !important;
  margin-left: -26px !important;
  margin-right: -26px !important;
  padding-left: 26px !important;
  padding-right: 26px !important;
  color: red !important;
  font-weight: bold !important;
  animation: fadeInValidation 0.5s ease forwards;
  border-radius: 0 !important;
}

@keyframes fadeInValidation {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.delivery-window-errors {
  color: $hotpink;
  margin-top: 1rem;
}

.delivery-window-date {
  flex: 1;
  border: 1px solid $black;
  text-align: center;
  cursor: pointer;
  & + .delivery-window-date {
    border-left: none;
  }
  &.selected {
    background: $black;
    color: white;
  }
  p {
    margin-bottom: 0;
  }
}

.delivery-window-container {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-gap: 4px;
  .delivery-window {
    font-size: 14px;
    border-radius: 4px;
    cursor: pointer;
    padding: 0.3rem 0.5rem;
    border: 1px solid $black;
    text-align: center;
    font-weight: bold;
    &.selected {
      background: $black;
      color: white;
    }
    &.processing {
      cursor: not-allowed;
    }
  }
}

.input-icon {
  padding-right: 12px;
  background-size: 20px;
  background-repeat: no-repeat;
  background-position: 2% 50%;
  padding-left: 40px !important;
  &.order {
    background-image: url(../../../images/icons/order.svg);
  }
  &.meal-plan {
    background-image: url(../../../images/icons/meal-plans.svg);
  }
  &.delivery {
    background-image: url(../../../images/icons/truck.svg);
  }
}

// same class used for adding specificity over input[type='text']
.cardholder-name.cardholder-name {
  padding: 12px;
  margin-bottom: 20px;
  background-image: url(../../../images/icons/user-grey.svg);
  background-repeat: no-repeat;
  background-position: 2% 50%;
  padding: 12px;
  padding-left: 42px !important;
  background-size: 26px;
  font-size: 14px;
  width: 100%;
  box-sizing: border-box;
  border: 1px solid #b4b4b4;
  cursor: text;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  font-family: sans-serif;
  color: rgb(38,28,21);
  &::placeholder {
    color: #888;
  }
}

a.invalid {
  color: $primary;
  text-decoration: underline;
}
.invalid {
  $invalid: #fa5361;
  color: $invalid;
  font-weight: bold;
  &--input {
    border: 1px solid $invalid !important;
    &__no-sides {
      border: 1px solid $invalid !important;
      border-left: none !important;
      border-right: none !important;
      border-radius: 0 !important;
      border-radius: 8px;
    }
  }
  &--button {
    background: $invalid !important;
  }
  &--number-button {
    border: 1px solid $invalid !important;
    border-radius: 0 !important;
    &.left {
      border-right: none !important;
    }
    &.right {
      border-left: none !important;
    }
  }
}

.number-of-people.number-of-people.number-of-people {
  background-image: url(../../../images/icons/team.svg);
  background-size: 20px;
  background-repeat: no-repeat;
  background-position: 10% center;
  transition: background-position 0.2s ease;
  padding-left: 10px;
  text-align: center;
  &.empty {
    border-left: none;
    border-right: none;
  }
}

.payment-input {
  display: flex;
  align-items: center;
  & + .payment-input {
    margin-left: 12px;
  }
}

.checkout-input.checkout-input {
  font-family: "Museo Sans";
  font-size: 14px;
  border: 1px solid #cecece;
  box-shadow: none;
  padding: 12px;
  padding-left: 40px;
  background-repeat: no-repeat;
  background-size: 18px;
  background-position: 12px center;
  border-radius: 8px;
  color: black;
  &.invalid {
    border: 2px solid red;
  }
  &.name {
    background-image: url(../../../images/icons/user-filled.svg)
  }
  &.phone {
    background-image: url(../../../images/icons/phone-filled.svg)
  }
  &.company {
    background-image: url(../../../images/icons/company-filled.svg)
  }
  &.marker {
    background-image: url(../../../images/icons/marker.svg)
  }
  &.email {
    background-image: url(../../../images/icons/email.svg)
  }
  &.account {
    background-position: 98% center;
    padding-left: 12px;
    background-size: 16px;
    margin-bottom: 30px;
  }
  &.pantry-manager {
    background-image: url(../../../images/icons/user.svg)
  }
}
textarea.checkout-input.checkout-input {
  background-position: 12px 12%;
}

.checkout-success-details {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.checkout-thank-you {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  margin-top: 20px;
}

.success-checkmark {
  width: 40px;
  display: inline-block;
  margin-bottom: 0;
  margin-right: 16px;
}
.success-message {
  color: #5ec9bd;
  font-family: $body-font;
  font-weight: bold;
  font-size: 24px !important;
  margin-bottom: 0;
}

.instructions-reminder {
  font-style: italic;
}

.path {
  stroke-dasharray: 1000;
  stroke-dashoffset: 0;
  &.circle {
    animation: dash 1.5s ease-in-out;
  }
  &.line {
    stroke-dashoffset: 1000;
    animation: dash 1.5s .35s ease-in-out forwards;
  }
  &.check {
    stroke-dashoffset: -100;
    animation: dash-check 1.5s .35s ease-in-out forwards;
  }
}

.order-map {
  margin: 20px 0;
  img{
    width: 100%;
  }
  &--small {
    max-width: 600px;
  }
}


@keyframes dash {
  0% {
    stroke-dashoffset: 1000;
  }
  100% {
    stroke-dashoffset: 0;
  }
}

@keyframes dash-check {
  0% {
    stroke-dashoffset: -100;
  }
  100% {
    stroke-dashoffset: 900;
  }
}

@keyframes blink {
  0% { opacity: .2; }
  20% { opacity: 1; }
  100% { opacity: .2; }
}

.loading-dots span {
  animation-name: blink;
  animation-duration: 1.4s;
  animation-iteration-count: infinite;
  animation-fill-mode: both;
}

.loading-dots span:nth-child(2) {
  animation-delay: .2s;
}

.loading-dots span:nth-child(3) {
  animation-delay: .4s;
}

.meal-plan-docket {
  border-bottom: 2px solid rgb(234, 234, 234);
  padding-bottom: 8px;
  h3 {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 8px;
  }
  p {
    margin-bottom: 4px;
    span {
      font-weight: bold;
    }
  }
  div {
    display: flex;
    justify-content: space-between;
  }
  .edit-details {
    margin-left: auto;
    color: rgb(31, 158, 134);
    font-weight: bold;
    font-size: 14px;
    cursor: pointer;
  }
}

.cancel-buttons {
  display: flex;
  flex-direction: column;
  .button {
    width: 100%;
    margin-bottom: 12px;
    &.cancel {
      background: #e42217;
      color: white;
      &:hover {
        background: darken(#e42217, 5%);
      }
    }
  }
}

.toggle-checkbox {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
  margin-left: auto;
  input {
    opacity: 0;
    width: 0;
    height: 0;
  }
  &__switch {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #46607B;
    transition: .4s;
    border-radius: 34px;
  }
  &__switch:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
  }

  input:checked + &__switch {
    background-color: $primary;
  }

  input:focus + &__switch {
    box-shadow: 0 0 1px $primary;
  }

  input:checked + &__switch:before {
    transform: translateX(17px);
  }

  input:disabled + &__switch {
    background-color: grey;
    cursor: not-allowed;
  }
}

.whats-this {
  cursor: pointer;
  text-decoration: underline dotted;
  text-transform: none;
  font-size: 12px;
  padding-left: 8px;
  font-family: $heading-font;
}

@import '../CustomOrder/CustomOrder';
